<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name', 'Liquid N Lights') }}</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=montserrat:400,500,600,700,800,900|open-sans:300,400,500,600,700" rel="stylesheet" />

    <!-- TailwindCSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Vue.js CDN -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>

    <style>
        /* Custom styles */
        .gradient-text {
            background: linear-gradient(135deg, #ec4899, #06b6d4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .animate-gradient {
            background: linear-gradient(-45deg, #ec4899, #06b6d4, #8b5cf6, #f59e0b);
            background-size: 400% 400%;
            animation: gradient 15s ease infinite;
        }
        @keyframes gradient {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
        .loader {
            width: 60px;
            height: 60px;
            border: 3px solid rgba(255, 255, 255, 0.1);
            border-top: 3px solid #ec4899;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body class="bg-gray-900 text-white">
    <div id="app">
        <!-- Loading state -->
        <div v-if="!mounted" class="min-h-screen bg-gray-900 flex items-center justify-center">
            <div class="text-center">
                <div class="loader"></div>
                <div class="gradient-text text-xl font-bold mt-4">Loading Liquid N Lights...</div>
            </div>
        </div>
        
        <!-- Main App -->
        <div v-else class="min-h-screen">
            <!-- Navigation -->
            <nav class="fixed top-0 left-0 right-0 z-50 bg-gray-900/95 backdrop-blur-sm border-b border-gray-800">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div class="flex justify-between items-center h-16">
                        <!-- Logo -->
                        <div class="flex items-center space-x-2">
                            <div class="w-8 h-8 bg-gradient-to-r from-pink-500 to-cyan-500 rounded-lg flex items-center justify-center">
                                <span class="text-white font-bold text-sm">LN</span>
                            </div>
                            <span class="text-xl font-bold gradient-text">Liquid N Lights</span>
                        </div>

                        <!-- Navigation Links -->
                        <div class="hidden md:flex items-center space-x-8">
                            <a href="#" @click="currentPage = 'home'" :class="currentPage === 'home' ? 'text-pink-400' : 'text-gray-300 hover:text-white'" class="transition-colors">Home</a>
                            <a href="#" @click="currentPage = 'events'" :class="currentPage === 'events' ? 'text-pink-400' : 'text-gray-300 hover:text-white'" class="transition-colors">Events</a>
                            <a href="#" class="text-gray-300 hover:text-white transition-colors">Gallery</a>
                            <a href="#" class="text-gray-300 hover:text-white transition-colors">About</a>
                            <button class="bg-gradient-to-r from-pink-500 to-cyan-500 text-white px-6 py-2 rounded-lg font-semibold hover:shadow-lg hover:shadow-pink-500/25 transition-all">
                                Book Now
                            </button>
                        </div>
                    </div>
                </div>
            </nav>

            <!-- Main Content -->
            <main class="pt-16">
                <!-- Home Page -->
                <div v-if="currentPage === 'home'">
                    <!-- Hero Section -->
                    <section class="relative h-screen flex items-center justify-center overflow-hidden">
                        <div class="absolute inset-0 bg-gradient-to-r from-gray-900 via-purple-900 to-gray-900">
                            <div class="absolute inset-0 bg-black/50"></div>
                        </div>

                        <!-- Hero Content -->
                        <div class="relative z-10 text-center max-w-4xl mx-auto px-4">
                            <h1 class="text-5xl md:text-7xl font-bold mb-6 animate-gradient bg-clip-text text-transparent">
                                Liquid N Lights
                            </h1>
                            <p class="text-xl md:text-2xl text-gray-300 mb-8 max-w-2xl mx-auto">
                                Interactive art installation that combines the nostalgia of Lite Brite with modern Instagram culture
                            </p>
                            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                                <button @click="currentPage = 'events'" class="bg-gradient-to-r from-pink-500 to-cyan-500 text-white px-8 py-4 rounded-lg font-semibold text-lg hover:shadow-lg hover:shadow-pink-500/25 transform hover:scale-105 transition-all">
                                    Explore Events
                                </button>
                                <button class="border-2 border-white/30 text-white px-8 py-4 rounded-lg font-semibold text-lg hover:bg-white/10 transition-all">
                                    Learn More
                                </button>
                            </div>
                        </div>
                    </section>

                    <!-- Featured Events Section -->
                    <section class="py-20 bg-gray-800">
                        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                            <div class="text-center mb-16">
                                <h2 class="text-4xl font-bold text-white mb-4">Featured Events</h2>
                                <p class="text-xl text-gray-400 max-w-2xl mx-auto">
                                    Discover our upcoming interactive art experiences
                                </p>
                            </div>

                            <!-- Events Grid -->
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                                <div v-for="event in featuredEvents" :key="event.id" 
                                     class="bg-gray-700 rounded-xl overflow-hidden hover:transform hover:scale-105 transition-all duration-300 hover:shadow-xl hover:shadow-pink-500/20">
                                    
                                    <!-- Event Image -->
                                    <div class="relative h-48 bg-gradient-to-br from-pink-500 to-cyan-500 overflow-hidden">
                                        <div class="w-full h-full flex items-center justify-center">
                                            <span class="text-white text-2xl font-bold">{{ event.title.charAt(0) }}</span>
                                        </div>
                                        
                                        <!-- Category Badge -->
                                        <div class="absolute top-4 left-4">
                                            <span class="bg-black/50 text-white px-3 py-1 rounded-full text-sm font-medium">
                                                {{ event.category }}
                                            </span>
                                        </div>
                                        
                                        <!-- Featured Badge -->
                                        <div class="absolute top-4 right-4">
                                            <span class="bg-gradient-to-r from-pink-500 to-cyan-500 text-white px-3 py-1 rounded-full text-sm font-bold">
                                                Featured
                                            </span>
                                        </div>
                                    </div>

                                    <!-- Event Details -->
                                    <div class="p-6">
                                        <h3 class="text-xl font-bold text-white mb-2">{{ event.title }}</h3>
                                        <p class="text-gray-400 mb-4">{{ event.description }}</p>
                                        
                                        <!-- Event Info -->
                                        <div class="space-y-2 mb-6">
                                            <div class="flex items-center text-gray-300">
                                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                                </svg>
                                                <span class="text-sm">{{ event.date }}</span>
                                            </div>
                                            <div class="flex items-center text-gray-300">
                                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                </svg>
                                                <span class="text-sm">{{ event.venue }}</span>
                                            </div>
                                            <div class="flex items-center text-gray-300">
                                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                                </svg>
                                                <span class="text-sm">Starting from ₹{{ event.price.toLocaleString() }}</span>
                                            </div>
                                        </div>

                                        <!-- Action Buttons -->
                                        <div class="flex gap-3">
                                            <button class="flex-1 bg-gradient-to-r from-pink-500 to-cyan-500 text-white text-center py-2 px-4 rounded-lg font-semibold hover:shadow-lg hover:shadow-pink-500/25 transition-all">
                                                View Details
                                            </button>
                                            <button class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-500 transition-colors">
                                                Quick Book
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>
                </div>

                <!-- Events Page -->
                <div v-if="currentPage === 'events'" class="min-h-screen">
                    <!-- Page Header -->
                    <section class="bg-gradient-to-r from-gray-900 via-purple-900 to-gray-900 py-20">
                        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
                            <h1 class="text-4xl md:text-6xl font-bold text-white mb-4">
                                Discover Events
                            </h1>
                            <p class="text-xl text-gray-300 max-w-2xl mx-auto">
                                Find the perfect interactive art experience for your next celebration
                            </p>
                        </div>
                    </section>

                    <!-- Events List -->
                    <section class="py-12 bg-gray-800">
                        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                            <div class="text-center mb-8">
                                <h2 class="text-2xl font-bold text-white mb-4">All Events</h2>
                                <p class="text-gray-400">{{ allEvents.length }} events available</p>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                                <div v-for="event in allEvents" :key="event.id" 
                                     class="bg-gray-700 rounded-xl overflow-hidden hover:transform hover:scale-105 transition-all duration-300 hover:shadow-xl hover:shadow-pink-500/20">
                                    
                                    <!-- Event Image -->
                                    <div class="relative h-48 bg-gradient-to-br from-pink-500 to-cyan-500 overflow-hidden">
                                        <div class="w-full h-full flex items-center justify-center">
                                            <span class="text-white text-2xl font-bold">{{ event.title.charAt(0) }}</span>
                                        </div>
                                        
                                        <!-- Category Badge -->
                                        <div class="absolute top-4 left-4">
                                            <span class="bg-black/50 text-white px-3 py-1 rounded-full text-sm font-medium">
                                                {{ event.category }}
                                            </span>
                                        </div>
                                    </div>

                                    <!-- Event Details -->
                                    <div class="p-6">
                                        <h3 class="text-xl font-bold text-white mb-2">{{ event.title }}</h3>
                                        <p class="text-gray-400 mb-4">{{ event.description }}</p>
                                        
                                        <!-- Event Info -->
                                        <div class="space-y-2 mb-6">
                                            <div class="flex items-center text-gray-300">
                                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                                </svg>
                                                <span class="text-sm">{{ event.date }}</span>
                                            </div>
                                            <div class="flex items-center text-gray-300">
                                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                </svg>
                                                <span class="text-sm">{{ event.venue }}</span>
                                            </div>
                                            <div class="flex items-center text-gray-300">
                                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                                </svg>
                                                <span class="text-sm">Starting from ₹{{ event.price.toLocaleString() }}</span>
                                            </div>
                                        </div>

                                        <!-- Action Buttons -->
                                        <div class="flex gap-3">
                                            <button class="flex-1 bg-gradient-to-r from-pink-500 to-cyan-500 text-white text-center py-2 px-4 rounded-lg font-semibold hover:shadow-lg hover:shadow-pink-500/25 transition-all">
                                                View Details
                                            </button>
                                            <button class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-500 transition-colors">
                                                Book Now
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>
                </div>
            </main>

            <!-- Footer -->
            <footer class="bg-gray-800 border-t border-gray-700 mt-20">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
                    <div class="text-center">
                        <div class="flex items-center justify-center space-x-2 mb-4">
                            <div class="w-8 h-8 bg-gradient-to-r from-pink-500 to-cyan-500 rounded-lg flex items-center justify-center">
                                <span class="text-white font-bold text-sm">LN</span>
                            </div>
                            <span class="text-xl font-bold gradient-text">Liquid N Lights</span>
                        </div>
                        <p class="text-gray-400 mb-4">
                            Interactive art installation that combines the nostalgia of Lite Brite with modern Instagram culture.
                        </p>
                        <div class="border-t border-gray-700 pt-8 text-gray-400">
                            <p>&copy; 2025 Liquid N Lights. All rights reserved.</p>
                        </div>
                    </div>
                </div>
            </footer>
        </div>
    </div>

    <script>
        const { createApp } = Vue;
        
        createApp({
            data() {
                return {
                    mounted: false,
                    currentPage: 'home',
                    featuredEvents: [
                        {
                            id: 1,
                            title: 'Liquid N Lights Interactive Art Experience',
                            description: 'Interactive art installation with glowing drink-filled bulbs',
                            date: 'Aug 15, 2025 - 6:00 PM',
                            venue: 'Downtown Art Gallery',
                            category: 'Art Fairs',
                            price: 2500
                        },
                        {
                            id: 2,
                            title: 'Corporate Gala Experience',
                            description: 'Elegant corporate event with interactive art elements',
                            date: 'Aug 22, 2025 - 7:00 PM',
                            venue: 'Business Center',
                            category: 'Corporate',
                            price: 3500
                        },
                        {
                            id: 3,
                            title: 'Wedding Reception Art Installation',
                            description: 'Romantic wedding setup with customized light displays',
                            date: 'Aug 29, 2025 - 5:00 PM',
                            venue: 'Garden Venue',
                            category: 'Weddings',
                            price: 5500
                        }
                    ],
                    allEvents: []
                }
            },
            mounted() {
                setTimeout(() => {
                    this.mounted = true;
                    this.allEvents = [
                        ...this.featuredEvents,
                        {
                            id: 4,
                            title: 'Birthday Party Special',
                            description: 'Fun and colorful setup perfect for birthday celebrations',
                            date: 'Sep 5, 2025 - 4:00 PM',
                            venue: 'Party Hall',
                            category: 'Parties',
                            price: 2000
                        },
                        {
                            id: 5,
                            title: 'Art Fair Exhibition',
                            description: 'Large scale installation for art fair visitors',
                            date: 'Sep 12, 2025 - 10:00 AM',
                            venue: 'Convention Center',
                            category: 'Art Fairs',
                            price: 1500
                        },
                        {
                            id: 6,
                            title: 'Launch Party Spectacular',
                            description: 'Product launch event with stunning visual effects',
                            date: 'Sep 19, 2025 - 8:00 PM',
                            venue: 'Tech Hub',
                            category: 'Corporate',
                            price: 4000
                        }
                    ];
                    
                    // Load events from API
                    this.loadEvents();
                }, 1000);
            },
            methods: {
                async loadEvents() {
                    try {
                        const response = await axios.get('/api/public/events');
                        if (response.data.success) {
                            console.log('Events loaded from API:', response.data.data);
                        }
                    } catch (error) {
                        console.log('Using mock data - API not available');
                    }
                }
            }
        }).mount('#app');
    </script>
</body>
</html>
