<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AuthController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Public authentication routes
Route::prefix('auth')->group(function () {
    Route::post('/admin/login', [AuthController::class, 'adminLogin']);
    Route::post('/magic-link', [AuthController::class, 'requestMagicLink']);
    Route::post('/verify-magic-link', [AuthController::class, 'verifyMagicLink']);
});

// Protected routes
Route::middleware('auth:sanctum')->group(function () {
    Route::get('/user', function (Request $request) {
        return $request->user()->load('roles', 'permissions');
    });
    
    Route::post('/logout', function (Request $request) {
        $request->user()->currentAccessToken()->delete();
        return response()->json(['message' => 'Logged out successfully']);
    });
});

// Admin routes (require authentication and admin roles)
Route::middleware(['auth:sanctum', 'role:Super Admin|Manager|Ticket Booker|Scanner'])->prefix('admin')->group(function () {
    // Events management
    Route::apiResource('events', App\Http\Controllers\Admin\EventController::class);
    Route::post('events/{event}/duplicate', [App\Http\Controllers\Admin\EventController::class, 'duplicate']);
    Route::get('events/{event}/analytics', [App\Http\Controllers\Admin\EventController::class, 'analytics']);

    // Bookings management
    Route::apiResource('bookings', App\Http\Controllers\Admin\BookingController::class);

    // CMS management
    Route::apiResource('cms', App\Http\Controllers\Admin\CmsController::class);
    Route::post('cms/{cms}/duplicate', [App\Http\Controllers\Admin\CmsController::class, 'duplicate']);
    Route::get('cms/menu/pages', [App\Http\Controllers\Admin\CmsController::class, 'menuPages']);
    Route::post('cms/import/liquid-n-lights', [App\Http\Controllers\Admin\CmsController::class, 'importLiquidNLights']);

    // API Settings (Super Admin only)
    Route::middleware('role:Super Admin')->group(function () {
        Route::apiResource('api-settings', App\Http\Controllers\Admin\ApiSettingController::class);
        Route::post('api-settings/{apiSetting}/test', [App\Http\Controllers\Admin\ApiSettingController::class, 'test']);
    });
});

// Public API routes (for frontend)
Route::prefix('public')->group(function () {
    // Public events listing
    // Public event details
    // Public booking creation
    // Public ticket verification
});
